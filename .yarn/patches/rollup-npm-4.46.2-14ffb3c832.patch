diff --git a/dist/es/shared/node-entry.js b/dist/es/shared/node-entry.js
index fd13da8f79d3802130a76af0192563006c30d2b5..a76b7a03b75e50669bfc012637735cf13e25b076 100644
--- a/dist/es/shared/node-entry.js
+++ b/dist/es/shared/node-entry.js
@@ -14976,7 +14976,9 @@ class ModuleScope extends ChildScope {
         return super.addDeclaration(identifier, context, init, destructuredInitPath, kind);
     }
     addExportDefaultDeclaration(name, exportDefaultDeclaration, context) {
+        if (this.context.fileName.endsWith('.css')) name = 'default';
         const variable = new ExportDefaultVariable(name, exportDefaultDeclaration, context);
+        if (this.context.fileName.endsWith('.css')) variable.hasId = true;
         this.variables.set('default', variable);
         return variable;
     }
@@ -17186,6 +17188,7 @@ class Module {
         const source = this.magicString.clone();
         this.ast.render(source, options);
         source.trim();
+        options.pluginDriver.hookReduceValueSync('jkmx$postRenderModule', null, [source, this], () => {});
         const { usesTopLevelAwait } = this.astContext;
         if (usesTopLevelAwait && options.format !== 'es' && options.format !== 'system') {
             return error(logInvalidFormatForTopLevelAwait(this.id, options.format));
@@ -18256,6 +18259,8 @@ class Chunk {
         }
         if (this.outputOptions.preserveModules || (this.facadeModule && this.facadeModule.info.isEntry))
             this.exportMode = getExportMode(this, this.outputOptions, this.facadeModule.id, this.inputOptions.onLog);
+        if (this.manualChunkAlias?.endsWith('@css'))
+            this.exportMode = 'default';
     }
     generateFacades() {
         const facades = [];
@@ -18361,7 +18366,7 @@ class Chunk {
         return this.fileName || this.getPreliminaryFileName().fileName;
     }
     getImportPath(importer) {
-        return escapeId(getImportPath(importer, this.getFileName(), this.outputOptions.format === 'amd' && !this.outputOptions.amd.forceJsExtensionForImports, true));
+        return this.name ?? escapeId(getImportPath(importer, this.getFileName(), this.outputOptions.format === 'amd' && !this.outputOptions.amd.forceJsExtensionForImports, true));
     }
     getPreliminaryFileName() {
         if (this.preliminaryFileName) {
@@ -18927,7 +18932,9 @@ class Chunk {
             renderedDependencies.set(dependency, {
                 attributes: dependency instanceof ExternalChunk
                     ? dependency.getImportAttributes(this.snippets)
-                    : null,
+                    : Object.keys(dependency.orderedModules[0].info.attributes).length
+                      ? formatAttributes(dependency.orderedModules[0].info.attributes, this.snippets)
+                      : null,
                 defaultVariableName: dependency.defaultVariableName,
                 globalName: dependency instanceof ExternalChunk &&
                     (this.outputOptions.format === 'umd' || this.outputOptions.format === 'iife') &&
@@ -20264,8 +20271,9 @@ async function transformChunk(magicString, fileName, usedModules, chunkGraph, op
             resultingFile = resolve$1(dir, fileName);
         else
             resultingFile = resolve$1(fileName);
-        const decodedMap = magicString.generateDecodedMap({});
+        const decodedMap = magicString.generateDecodedMap({ hires: env.NODE_ENV === 'production' });
         map = collapseSourcemaps(resultingFile, decodedMap, usedModules, sourcemapChain, sourcemapExcludeSources, log);
+        await outputPluginDriver.hookSeq('jkmx$updateChunkMap', [map, resultingFile, chunkGraph[fileName]]);
         for (let sourcesIndex = 0; sourcesIndex < map.sources.length; ++sourcesIndex) {
             let sourcePath = map.sources[sourcesIndex];
             const sourcemapPath = `${resultingFile}.map`;
@@ -20428,7 +20436,7 @@ function emitSourceMapAndGetComment(fileName, map, pluginDriver, { sourcemap, so
             type: 'asset'
         });
     }
-    return sourcemap === 'hidden' ? '' : `//# ${SOURCEMAPPING_URL}=${url}\n`;
+    return sourcemap === 'hidden' ? '' : `//# ${SOURCEMAPPING_URL}=${url}`;
 }
 function calculateDebugIdAndGetComment(code, map) {
     const hash = hasherByType.hex(code);
@@ -20460,7 +20468,7 @@ class Bundle {
         this.pluginDriver.setOutputBundle(outputBundle, this.outputOptions);
         try {
             timeStart('initialize render', 2);
-            await this.pluginDriver.hookParallel('renderStart', [this.outputOptions, this.inputOptions]);
+            await this.pluginDriver.hookParallel('renderStart', [this.outputOptions, this.inputOptions, this]);
             timeEnd('initialize render', 2);
             timeStart('generate chunks', 2);
             const getHashPlaceholder = getHashPlaceholderGenerator();
@@ -20474,6 +20482,7 @@ class Bundle {
                 chunk.inlineTransitiveImports();
             }
             timeEnd('generate chunks', 2);
+            await this.pluginDriver.hookParallel('jkmx$postGenerate', [this, chunks]);
             await renderChunks(chunks, outputBundle, this.pluginDriver, this.outputOptions, this.inputOptions.onLog);
         }
         catch (error_) {
@@ -21428,9 +21437,10 @@ class ModuleLoader {
         if (resolveIdResult !== false && !this.options.external(id, importer, true)) {
             return null;
         }
+        const id_ = (x => x === true ? id : x)(this.options.external(id, importer, true));
         return {
-            external: isNotAbsoluteExternal(id, source, makeAbsoluteExternalsRelative) || 'absolute',
-            id
+            external: isNotAbsoluteExternal(id_, source, makeAbsoluteExternalsRelative) || 'absolute',
+            id: id_
         };
     }
     getResolveDynamicImportPromises(module) {
diff --git a/dist/shared/rollup.js b/dist/shared/rollup.js
index edad94ba128cc171579371fcf51b4847ddf374ca..acece1d432ac98e701c79eb40cedc7c8c2e9bb55 100644
--- a/dist/shared/rollup.js
+++ b/dist/shared/rollup.js
@@ -23740,9 +23740,12 @@ function defineConfig(options) {
 }
 
 exports.bold = bold;
+exports.calculateDebugIdAndGetComment = calculateDebugIdAndGetComment;
+exports.collapseSourcemap = collapseSourcemap;
 exports.commandAliases = commandAliases;
 exports.createFilter = createFilter;
 exports.cyan = cyan;
+exports.decodedSourcemap = decodedSourcemap;
 exports.defineConfig = defineConfig;
 exports.ensureArray = ensureArray$1;
 exports.getAugmentedNamespace = getAugmentedNamespace;
