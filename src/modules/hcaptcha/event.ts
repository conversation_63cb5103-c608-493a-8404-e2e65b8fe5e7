export const
	CHALLENGE_PASSED = 'challenge-passed',
	CHALLENGE_ESCAPED = 'challenge-escaped',
	CHALLENGE_CLOSED = 'challenge-closed',
	CHALLENGE_EXPIRED = 'challenge-expired';

const
	TOUCHES = {
		touchstart: 'ts',
		touchend: 'te',
		touchmove: 'tm',
		touchcancel: 'tc'
	},
	MOUSES = {
		mousedown: 'md',
		mouseup: 'mu',
		mousemove: 'mm'
	},
	POINTERS = {
		pointermove: 'pm'
	},
	KEYS = {
		keydown: 'kd',
		keyup: 'ku'
	},
	DEVICE_MOTIONS = {
		devicemotion: 'dm'
	};

export const
	pickMouse = (key: keyof typeof MOUSES, callback: Function) => (event: MouseEvent) => callback(MOUSES[key], [event.pageX, event.pageY, Date.now()]),
	pickPointer = (key: keyof typeof POINTERS, callback: Function) => (event: PointerEvent) => {
		if (event.getCoalescedEvents) {
			const events = event.getCoalescedEvents(), time = Date.now();
			for (const e of events) {
				callback(POINTERS[key], [e.x, e.y, time]);
			}
		}
	},
	pickTouch = (key: keyof typeof TOUCHES, callback: Function) => (event: TouchEvent) => {
		try {
			let touches: TouchList = <any>[];
			if (event.touches && event.touches.length >= 1) {
				touches = event.touches;
			} else if (event.changedTouches && event.changedTouches.length >= 1) {
				touches = event.changedTouches;
			}
			const p = [];
			for (const touch of touches) {
				p.push(touch.identifier, touch.pageX, touch.pageY);
			}
			p.push(Date.now())
			callback(TOUCHES[key], p);
		} catch {
			callback(TOUCHES[key], []);
		}
	},
	pickKey = (key: keyof typeof KEYS, callback: Function) => (event: KeyboardEvent) => callback(KEYS[key], [event.which ?? event.keyCode, Date.now()]),
	pickDevice = (key: keyof typeof DEVICE_MOTIONS, callback: Function) => {
		let prev = [0, 0, 0, 0, 0, 0];
		return (event: DeviceMotionEvent) => {
			if (event.acceleration?.x == null)
				(<any>event).acceleration = { x: 0, y: 0, z: 0 };
			if (event.rotationRate?.alpha == null)
				(<any>event).rotationRate = { alpha: 0, beta: 0, gamma: 0 };
			const motion = [event.acceleration!.x!, event.acceleration!.y!, event.acceleration!.z!, event.rotationRate!.alpha!, event.rotationRate!.beta!, event.rotationRate!.gamma!, Date.now()];
			let o = 0;
			for (let i = 0; i < 6; ++i) {
				o += Math.abs(prev[i] - motion[i]);
			}
			prev = motion;
			if (o > 0) {
				callback(DEVICE_MOTIONS[key], motion);
			}
		}
	};
