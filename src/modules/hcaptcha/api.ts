import { Browser } from './browserInfo';
import { errorInvalidID, errorMissingCaptcha, INVALID_CAPTCHA_ID, MISSING_CAPTCHA, MISSING_SITEKEY } from './errors';
import { CHALLENGE_CLOSED, CHALLENGE_ESCAPED } from './event';
import { add, each, getById, getByIndex, getSessions, Node, pushSession, remove as removeNode, updateTranslation } from './node';
import { recorder } from './recorder';
import { System } from './systemInfo';
import { getLocale, loadLocale, resolveLocale, setLocale } from './translator';

function appendWidget(container: HTMLElement, customPrompt?: string) {
	container.style.width = '304px';
	container.style.height = '78px';
	container.style.backgroundColor = '#f9e5e5';
	container.style.position = 'relative';
	container.innerHTML = '';
	const div = document.createElement('div');
	div.style.width = '284px';
	div.style.position = 'absolute';
	div.style.top = '12px';
	div.style.left = '10px';
	div.style.color = '#7c0a06';
	div.style.fontSize = '14px';
	div.style.fontWeight = 'normal';
	div.style.lineHeight = '18px';
	div.innerHTML = customPrompt ?? "Please <a style=\"color: inherit; text-decoration: underline; font: inherit\" target=\"_blank\" href=\"https://www.whatismybrowser.com/guides/how-to-update-your-browser/auto\">upgrade your browser</a> to complete this captcha.";
	container.appendChild(div);
}

let nonce = 0;

export function render(container: HTMLElement | string, config: ConfigRender) {
	if (typeof container === 'string')
		container = document.getElementById(container)!;
	if (container && container.nodeType === 1) {
		let challengeContainer = config['challenge-container'], flag = true;
		if (challengeContainer) {
			if (typeof challengeContainer === 'string')
				challengeContainer = document.getElementById(challengeContainer)!;
			flag = challengeContainer && challengeContainer.nodeType === 1;
		}
		if (flag) {
			const
				id = nonce++ + Math.random().toString(36).substring(2),
				configR = {
					sentry: true,
					reportapi: 'https://accounts.hcaptcha.com',
					recaptchacompat: true,
					custom: false,
					hl: getLocale(),
					tplinks: 'on',
					pat: 'on',
					pstissuer: 'https://pst-issuer.hcaptcha.com',
					theme: 'light',
					origin: window.location.origin,
					...config,
				};
			let node;
			try {
				node = new Node(container, id, configR);
			} catch (err: any) {
				let prompt = 'Your browser plugins or privacy policies are blocking the hCaptcha service. Please disable them for hCaptcha.com';
				if (err.cause === MISSING_SITEKEY) {
					prompt = 'hCaptcha has failed to initialize. Please see the developer tools console for more information.';
				}
				console.error(err.message);
				appendWidget(container, prompt);
				return;
			}
			if (config.callback) (node.onPass = <any>config.callback);
			if (config['expired-callback']) (node.onExpire = <any>config['expired-callback']);
			if (config['chalexpired-callback']) (node.onChalExpire = <any>config['chalexpired-callback']);
			if (config['open-callback']) (node.onOpen = <any>config['open-callback']);
			if (config['close-callback']) (node.onClose = <any>config['close-callback']);
			if (config['error-callback']) (node.onError = <any>config['error-callback']);
			try {
				recorder.setData('inv', configR.size === 'invisible');
				recorder.setData('size', configR.size);
				recorder.setData('theme', configR.theme);
				recorder.setData('pel', (container.outerHTML ?? '').replace(container.innerHTML, ''));
			} catch { }
			!function (_node_, _config_) {
				if ('invisible' === _config_.size)
					return;
				_node_.checkbox.chat.listen('checkbox-selected', t => {
					const n = 'enter' === t.action ? 'kb' : 'm';
					recorder.setData('exec', n);
					_node_.onReady(_node_.initChallenge, t);
				});
				_node_.checkbox.chat.listen('checkbox-loaded', n => {
					_node_.checkbox.location.bounding = _node_.checkbox.getBounding(),
						_node_.checkbox.location.tick = n,
						_node_.checkbox.location.offset = _node_.checkbox.getOffset(),
						_node_.checkbox.sendTranslation(_config_.hl)
				});
				if (_config_.mode === 'auto')
					_node_.onReady(() => execute(_node_.id), _config_);
			}(node, configR);
			!function (_node_, _config_) {
				function n(t, n) {
					if (t.locale) {
						const r = resolveLocale(t.locale);
						loadLocale(r).then(() => {
							if (n) {
								updateTranslation(_node_, r);
							} else {
								setLocale(r);
								nodes.each(e => updateTranslation(e, r));
							}
						}, () => { })
					}
				}
				_node_.challenge.chat.listen('site-setup', siteConfig => {
					const promise = _node_.setSiteConfig(siteConfig);
					_node_.challenge.onReady(() => promise.then(() => _node_.setReady(true)));
				});
				_node_.challenge.chat.listen("challenge-loaded", () => {
					_node_.challenge.setReady();
					_node_.challenge.sendTranslation(_config_.hl);
				});
				_node_.challenge.chat.answer('challenge-ready', (challenge, node) => _node_.displayChallenge(challenge).then(node.resolve));
				_node_.challenge.chat.listen('challenge-resize', () => _node_.resize(Browser.width(), Browser.height()));
				_node_.challenge.chat.listen(CHALLENGE_CLOSED, reason => {
					recorder.setData('lpt', Date.now());
					_node_.closeChallenge(reason);
				});
				_node_.challenge.chat.answer('get-url', e => e.resolve(window.location.href));
				_node_.challenge.chat.answer('getcaptcha-manifest', t => t.resolve(_node_.getGetCaptchaManifest()));
				_node_.challenge.chat.answer('check-api', e => e.resolve(recorder.getData()));
				_node_.challenge.chat.listen('challenge-key', t => pushSession(t.key, _node_.id));
				_node_.challenge.onOverlayClick(() => _node_.closeChallenge({ event: CHALLENGE_ESCAPED }));
				_node_.challenge.chat.listen('challenge-language', n);
				n({ locale: _config_.hl }, true);
				_node_.challenge.chat.answer('get-ac', e => e.resolve(false));
			}(node, configR);
			add(node);
			return id;
		} else
			console.log(`[hCaptcha] render: invalid challenge container '${challengeContainer}'.`);
	} else
		console.log(`[hCaptcha] render: invalid container '${container}'.`);
}

export function reset(id: string) {
	const node = id ? getById(id) : getByIndex(0);
	if (node) {
		return node.reset();
	}
	throw id ? errorInvalidID(id) : errorMissingCaptcha();
}

export function remove(id: string) {
	const node = id ? getById(id) : getByIndex(0);
	if (node) {
		removeNode(node);
		node.destroy();
	}
	throw id ? errorInvalidID(id) : errorMissingCaptcha();
}

export function execute(id?: string, config?: ConfigExecute) {
	if (typeof id === 'object' && !config) config = id, id = <any>null;
	config ??= {};
	let node;
	const isAsync = config.async === true, { promise, resolve, reject } = Promise.withResolvers<HCaptchaResponse>();
	if (node = id ? getById(id) : getByIndex(0)) {
		recorder.setData('exec', 'api');
		if (isAsync)
			node.setPromise({ resolve, reject });
		node.onReady(node.initChallenge, config);
	} else if (id) {
		if (!isAsync)
			throw errorInvalidID(id);
		reject(INVALID_CAPTCHA_ID)
	} else {
		if (!isAsync)
			throw errorMissingCaptcha();
		reject(MISSING_CAPTCHA)
	}
	if (isAsync)
		return promise;
}

export function getResponse(id: string) {
	const node = id ? getById(id) : getByIndex(0);
	if (node) {
		return node.checkbox.response ?? '';
	}
	throw id ? errorInvalidID(id) : errorMissingCaptcha();
}

export function getRespKey(id: string) {
	const node = id ? getById(id) : getByIndex(0);
	try {
		const session = getSessions();
		for (let i = session.length; --i >= 0;)
			if (session[i][1] === node!.id)
				return session[i][0];
	} catch { }
	return '';
}

export function close(id: string) {
	const node = id ? getById(id) : getByIndex(0);
	if (node) {
		return node.closeChallenge({ event: CHALLENGE_ESCAPED });
	}
	throw id ? errorInvalidID(id) : errorMissingCaptcha();
}

export function setData(id: string, config: ConfigSetData) {
	if (typeof id === 'object' && !config) config = id, id = <any>null;
	if (typeof config !== 'object')
		throw Error('[hCaptcha] invalid data supplied');
	const node = id ? getById(id) : getByIndex(0);
	if (node) {
		return node.onReady(node.challenge.setData.bind(node.challenge), config);
	}
	throw id ? errorInvalidID(id) : errorMissingCaptcha();
}

let aspectRatio = Browser.width() / Browser.height()
function resizeHandler() {
	const newRatio = Browser.width() / Browser.height(), updated = System.mobile && aspectRatio !== newRatio;
	aspectRatio = newRatio;
	resizeInner();
	each(node => node.visible && node.resize(Browser.width(), Browser.height(), updated));
}

function scrollHandler() {
	scrollInner();
	each(node => node.visible && node.position());
}

function scrollInner() {
	recorder.circBuffPush('xy', [Browser.scrollX(), Browser.scrollY(), document.documentElement.clientWidth / Browser.width(), Date.now()])
}

function resizeInner() {
	recorder.circBuffPush('wn', [Browser.width(), Browser.height(), System.dpr(), Date.now()])
}

setLocale(window.navigator.language);

{
	const locale = getLocale();
	if (locale !== 'en') {
		loadLocale(locale).then(() => each(node => updateTranslation(node, locale)));
	}
}

try {
	recorder.record();
	recorder.setData('sc', Browser.getScreenDimensions());
	recorder.setData('wi', Browser.getWindowDimensions());
	recorder.setData('nv', Browser.interrogateNavigator());
	recorder.setData('dr', document.referrer);
	resizeInner();
	scrollInner();
} catch { }

window.addEventListener('resize', resizeHandler);
window.addEventListener('scroll', scrollHandler);
