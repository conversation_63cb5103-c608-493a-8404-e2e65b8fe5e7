import { I18N_URL } from './env';

type TranslateTable = Record<string, string>;

const
	LANGUAGE_NAMES: Record<string, string> = {
		af: 'Afrikaans',
		sq: 'Albanian',
		am: 'Amharic',
		ar: 'Arabic',
		hy: 'Armenian',
		az: 'Azerbaijani',
		eu: 'Basque',
		be: 'Belarusian',
		bn: 'Bengali',
		bg: 'Bulgarian',
		bs: 'Bosnian',
		my: 'Burmese',
		ca: 'Catalan',
		ceb: 'Cebuano',
		zh: 'Chinese',
		'zh-CN': 'Chinese Simplified',
		'zh-TW': 'Chinese Traditional',
		co: 'Corsican',
		hr: 'Croatian',
		cs: 'Czech',
		da: 'Danish',
		nl: 'Dutch',
		en: 'English',
		eo: 'Esperanto',
		et: 'Estonian',
		fi: 'Finnish',
		fr: 'French',
		fy: 'Frisian',
		gd: 'Gaelic',
		gl: 'Galacian',
		ka: 'Georgian',
		de: 'German',
		el: 'Greek',
		gu: 'Gujurati',
		ht: 'Haitian',
		ha: 'Hausa',
		haw: 'Hawaiian',
		he: 'Hebrew',
		hi: 'Hindi',
		hmn: 'Hmong',
		hu: 'Hungarian',
		is: 'Icelandic',
		ig: 'Igbo',
		id: 'Indonesian',
		ga: 'Irish',
		it: 'Italian',
		ja: 'Japanese',
		jw: 'Javanese',
		kn: 'Kannada',
		kk: 'Kazakh',
		km: 'Khmer',
		rw: 'Kinyarwanda',
		ky: 'Kirghiz',
		ko: 'Korean',
		ku: 'Kurdish',
		lo: 'Lao',
		la: 'Latin',
		lv: 'Latvian',
		lt: 'Lithuanian',
		lb: 'Luxembourgish',
		mk: 'Macedonian',
		mg: 'Malagasy',
		ms: 'Malay',
		ml: 'Malayalam',
		mt: 'Maltese',
		mi: 'Maori',
		mr: 'Marathi',
		mn: 'Mongolian',
		ne: 'Nepali',
		no: 'Norwegian',
		ny: 'Nyanja',
		or: 'Oriya',
		fa: 'Persian',
		pl: 'Polish',
		'pt-BR': 'Portuguese (Brazil)',
		pt: 'Portuguese (Portugal)',
		ps: 'Pashto',
		pa: 'Punjabi',
		ro: 'Romanian',
		ru: 'Russian',
		sm: 'Samoan',
		sn: 'Shona',
		sd: 'Sindhi',
		si: 'Singhalese',
		sr: 'Serbian',
		sk: 'Slovak',
		sl: 'Slovenian',
		so: 'Somani',
		st: 'Southern Sotho',
		es: 'Spanish',
		su: 'Sundanese',
		sw: 'Swahili',
		sv: 'Swedish',
		tl: 'Tagalog',
		tg: 'Tajik',
		ta: 'Tamil',
		tt: 'Tatar',
		te: 'Teluga',
		th: 'Thai',
		tr: 'Turkish',
		tk: 'Turkmen',
		ug: 'Uyghur',
		uk: 'Ukrainian',
		ur: 'Urdu',
		uz: 'Uzbek',
		vi: 'Vietnamese',
		cy: 'Welsh',
		xh: 'Xhosa',
		yi: 'Yiddish',
		yo: 'Yoruba',
		zu: 'Zulu',
	},
	TRANSLATE_TABLE: Record<string, TranslateTable> = {
		zh: {
			'I am human': '我是人'
		},
		ar: {
			'I am human': 'أنا الإنسان'
		},
		af: {
			'I am human': 'Ek is menslike'
		},
		am: {
			'I am human': 'እኔ ሰው ነኝ'
		},
		hy: {
			'I am human': 'Ես մարդ եմ'
		},
		az: {
			'I am human': 'Mən insanam'
		},
		eu: {
			'I am human': 'Gizakia naiz'
		},
		bn: {
			'I am human': 'আমি মানব নই'
		},
		bg: {
			'I am human': 'Аз съм човек'
		},
		ca: {
			'I am human': 'Sóc humà'
		},
		hr: {
			'I am human': 'Ja sam čovjek'
		},
		cs: {
			'I am human': 'Jsem člověk'
		},
		da: {
			'I am human': 'Jeg er et menneske'
		},
		nl: {
			'I am human': 'Ik ben een mens'
		},
		et: {
			'I am human': 'Ma olen inimeste'
		},
		fi: {
			'I am human': 'Olen ihminen'
		},
		fr: {
			'I am human': 'Je suis humain'
		},
		gl: {
			'I am human': 'Eu son humano'
		},
		ka: {
			'I am human': 'მე ვარ ადამიანი'
		},
		de: {
			'I am human': 'Ich bin ein Mensch'
		},
		el: {
			'I am human': 'Είμαι άνθρωπος'
		},
		gu: {
			'I am human': 'હું માનવ છું'
		},
		iw: {
			'I am human': '. אני אנושי'
		},
		hi: {
			'I am human': 'मैं मानव हूं'
		},
		hu: {
			'I am human': 'Nem vagyok robot'
		},
		is: {
			'I am human': 'Ég er manneskja'
		},
		id: {
			'I am human': 'Aku manusia'
		},
		it: {
			'I am human': 'Sono un essere umano'
		},
		ja: {
			'I am human': '私は人間です'
		},
		kn: {
			'I am human': 'ನಾನು ಮಾನವನು'
		},
		ko: {
			'I am human': '사람입니다'
		},
		lo: {
			'I am human': 'ຂ້ອຍເປັນມະນຸດ'
		},
		lv: {
			'I am human': 'Es esmu cilvēks'
		},
		lt: {
			'I am human': 'Aš esu žmogaus'
		},
		ms: {
			'I am human': 'Saya manusia'
		},
		ml: {
			'I am human': 'ഞാൻ മനുഷ്യനാണ്'
		},
		mr: {
			'I am human': 'मी मानवी आहे'
		},
		mn: {
			'I am human': 'Би бол хүн'
		},
		no: {
			'I am human': 'Jeg er menneskelig'
		},
		fa: {
			'I am human': 'من انسانی هستم'
		},
		pl: {
			'I am human': 'Jestem człowiekiem'
		},
		pt: {
			'I am human': 'Sou humano'
		},
		ro: {
			'I am human': 'Eu sunt om'
		},
		ru: {
			'I am human': 'Я человек'
		},
		sr: {
			'I am human': 'Ja sam ljudski'
		},
		si: {
			'I am human': 'මම මිනිස්සු'
		},
		sk: {
			'I am human': 'Ja som človek'
		},
		sl: {
			'I am human': 'Jaz sem človeški'
		},
		es: {
			'I am human': 'Soy humano'
		},
		sw: {
			'I am human': 'Mimi ni binadamu'
		},
		sv: {
			'I am human': 'Jag är människa'
		},
		ta: {
			'I am human': 'நான் மனித'
		},
		te: {
			'I am human': 'నేను మనిషిని'
		},
		th: {
			'I am human': 'ผมมนุษย์'
		},
		tr: {
			'I am human': 'Ben bir insanım'
		},
		uk: {
			'I am human': 'Я людини'
		},
		ur: {
			'I am human': 'میں انسان ہوں'
		},
		vi: {
			'I am human': 'Tôi là con người'
		},
		zu: {
			'I am human': 'Ngingumuntu'
		},
	};

let locale: string | null = null;

export function translate(word: string, args?: Record<string, string>) {
	const dict = getBestTrans(TRANSLATE_TABLE);
	if (dict && dict[word]) word = dict[word];
	for (const key in args)
		word = word.replaceAll(`{{${key}}}`, args[key]);
	return word;
}

export function getBestTrans(table: Record<string, TranslateTable>) {
	let locale = getLocale();
	if (locale in table) return table[locale];
	locale = getShortLocale(locale);
	if (locale in table) return table[locale];
	if ('en' in table) return table.en;
	return null;
}

export function resolveLocale(locale: string) {
	switch (getShortLocale(locale)) {
		case 'in': return 'id';
		case 'iw': return 'he';
		case 'nb': return 'no';
		case 'ji': return 'yi';
		case 'jv': return 'jw';
		case 'me': return 'bs';
		default: return LANGUAGE_NAMES[locale] ? locale : 'en';
	}
}

export function getLocale() {
	return resolveLocale(locale ?? window.navigator.language);
}

export function setLocale(lang: string) {
	switch (lang) {
		case 'zh-Hans': return locale = 'zh-CN';
		case 'zh-Hant': return locale = 'zh-TW';
		default: return locale = lang;
	}
}

export function getShortLocale(long: string) {
	const dash = long.indexOf('-');
	return ~dash ? long.substring(0, dash) : long;
}

export function getLangName(lang: string) {
	return LANGUAGE_NAMES[lang];
}

export function isShortLocale(lang: string) {
	return lang.length === 2 || lang.length === 3;
}

export function addTable(lang: string, table: TranslateTable) {
	if (TRANSLATE_TABLE[lang]) {
		Object.assign(TRANSLATE_TABLE[lang], table);
	} else {
		TRANSLATE_TABLE[lang] = table;
	}
	return TRANSLATE_TABLE[lang];
}

export function getTable(lang: string) {
	return TRANSLATE_TABLE[lang];
}

export function addTables(tables: Record<string, TranslateTable>) {
	for (const lang in tables)
		addTable(lang, tables[lang]);
	return TRANSLATE_TABLE;
}

export function getTables() {
	return TRANSLATE_TABLE;
}

const loadedLocales = new Set(['en']);
export async function loadLocale(locale: string) {
	if (loadedLocales.has(locale))
		return;
	const response = await fetch(`${I18N_URL}/${locale}.json`).then(resp => resp.json());
	addTable(locale, response);
	loadedLocales.add(locale);
}
