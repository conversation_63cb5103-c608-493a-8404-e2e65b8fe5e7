{"imports": {"hcaptcha@@": "/ajax/libs/hcaptcha/api.mjs", "katex": "/ajax/libs/KaTeX/0.16.22/katex.mjs", "katex@css": "/ajax/libs/KaTeX/0.16.22/katex.min.css", "monaco-editor/": "https://esm.sh/v135/monaco-editor@0.52.2&no-bundle/", "monaco-editor@css": "/ajax/libs/monaco-editor/0.52.2/min/vs/editor/editor.main.css", "prop-types": "https://esm.sh/prop-types", "react": "https://esm.sh/react", "react-dom": "https://esm.sh/react-dom", "react-dom/client": "https://esm.sh/react-dom/client", "semantic@css": "/ajax/libs/fomantic-ui/2.9.4/semantic.min.css", "semantic-ui-react": "https://esm.sh/semantic-ui-react@%5E3.0.0-beta.2?deps=react@^19,react-dom@^19,react-is@^19&standalone", "socket.io-client": "/ajax/libs/socket.io/4.8.1/socket.io.esm.min.js", "https://esm.sh/node/buffer.mjs": "/js/polyfill/buffer.mjs", "https://esm.sh/node/process.mjs": "/js/polyfill/process.mjs", "index": "/js/pages/index.D2Z38MUz.mjs", "libs": "/js/libs.V8FxT-dX.mjs", "logout": "/js/pages/logout.aPb2MrpN.mjs", "preference": "/js/pages/preference.C2gC6k_T.mjs", "rot10451": "/js/pages/rot10451.D8lmvkNy.mjs", "chordle/list": "/js/pages/chordle/list.DJJmA5kE.mjs", "genkey": "/js/pages/genkey.DOzRAMXs.mjs", "login": "/js/pages/login.S8X376bu.mjs", "register": "/js/pages/register.BThcd3ed.mjs", "change": "/js/pages/change.CjltAI4r.mjs", "resetkey": "/js/pages/resetkey.XOqJAgvp.mjs", "chordle": "/js/pages/chordle.BvrRggp2.mjs", "leaderboard": "/js/leaderboard.UYDMUIj3.mjs", "ourtimes": "/js/pages/ourtimes.DEkdMbia.mjs", "catcoin": "/js/catcoin.5zr8qXu3.mjs", "hcaptcha": "/js/hcaptcha.CmnYryon.mjs", "libs@css": "/css/libs.BDz4MB2e.css", "monaco": "/js/monaco.BI8a9-2O.mjs", "monaco@css": "/css/monaco.CfsDtlrH.css", "socket": "/js/socket.C-bHfCZO.mjs", "react-katex": "/js/react-katex.D0rJMOu-.mjs", "wasm": "/js/wasm.DfR26pIk.mjs"}, "scopes": {"https://esm.sh/v135/": {"https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/base/browser/ui/actionbar/actionbar.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/base/browser/ui/aria/aria.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/base/browser/ui/button/button.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/base/browser/ui/codicons/codicon/codicon-modifiers.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/base/browser/ui/codicons/codicon/codicon.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/base/browser/ui/contextview/contextview.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/base/browser/ui/countBadge/countBadge.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/base/browser/ui/dropdown/dropdown.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/base/browser/ui/findinput/findInput.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/base/browser/ui/hover/hoverWidget.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/base/browser/ui/iconLabel/iconlabel.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/base/browser/ui/inputbox/inputBox.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/base/browser/ui/keybindingLabel/keybindingLabel.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/base/browser/ui/list/list.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/base/browser/ui/mouseCursor/mouseCursor.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/base/browser/ui/progressbar/progressbar.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/base/browser/ui/sash/sash.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/base/browser/ui/scrollbar/media/scrollbars.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/base/browser/ui/selectBox/selectBox.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/base/browser/ui/selectBox/selectBoxCustom.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/base/browser/ui/splitview/splitview.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/base/browser/ui/table/table.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/base/browser/ui/toggle/toggle.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/base/browser/ui/toolbar/toolbar.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/base/browser/ui/tree/media/tree.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/browser/controller/textAreaHandler.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/browser/services/hoverService/hover.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/browser/viewParts/blockDecorations/blockDecorations.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/browser/viewParts/currentLineHighlight/currentLineHighlight.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/browser/viewParts/decorations/decorations.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/browser/viewParts/glyphMargin/glyphMargin.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/browser/viewParts/indentGuides/indentGuides.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/browser/viewParts/lineNumbers/lineNumbers.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/browser/viewParts/lines/viewLines.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/browser/viewParts/linesDecorations/linesDecorations.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/browser/viewParts/margin/margin.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/browser/viewParts/marginDecorations/marginDecorations.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/browser/viewParts/minimap/minimap.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/browser/viewParts/overlayWidgets/overlayWidgets.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/browser/viewParts/rulers/rulers.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/browser/viewParts/scrollDecoration/scrollDecoration.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/browser/viewParts/selections/selections.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/browser/viewParts/viewCursors/viewCursors.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/browser/viewParts/whitespace/whitespace.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/browser/widget/codeEditor/editor.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/browser/widget/diffEditor/components/accessibleDiffViewer.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/browser/widget/diffEditor/style.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/browser/widget/markdownRenderer/browser/renderedMarkdown.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/browser/widget/multiDiffEditor/style.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/contrib/bracketMatching/browser/bracketMatching.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/contrib/codeAction/browser/lightBulbWidget.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/contrib/colorPicker/browser/colorPicker.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/contrib/dropOrPasteInto/browser/postEditWidget.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/contrib/find/browser/findOptionsWidget.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/contrib/find/browser/findWidget.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/contrib/folding/browser/folding.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/contrib/gotoError/browser/media/gotoErrorWidget.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/contrib/gotoSymbol/browser/peek/referencesWidget.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/contrib/hover/browser/hover.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/contrib/inlineCompletions/browser/hintsWidget/inlineCompletionsHintsWidget.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/contrib/inlineProgress/browser/inlineProgressWidget.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/contrib/message/browser/messageController.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/contrib/peekView/browser/media/peekViewWidget.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/contrib/symbolIcons/browser/symbolIcons.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/contrib/wordHighlighter/browser/highlightDecorations.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/contrib/zoneWidget/browser/zoneWidget.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/standalone/browser/quickInput/standaloneQuickInput.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/standalone/browser/standalone-tokens.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/platform/actionWidget/browser/actionWidget.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/platform/actions/browser/menuEntryActionViewItem.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/platform/quickinput/browser/media/quickInput.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/platform/severityIcon/browser/media/severityIcon.css.bundless.js": "/js/polyfill/empty.mjs"}}, "integrity": {"/ajax/libs/KaTeX/0.16.22/katex.min.css": "sha256-GQlRJzV+1tKf4KY6awAMkTqJ9/GWO3Zd03Fel8mFLnU=", "/ajax/libs/KaTeX/0.16.22/katex.mjs": "sha256-evGqXkhKSuiB4YH/l93/3OLeq+9CWW0meWWLn+TNRhU=", "/ajax/libs/fomantic-ui/2.9.4/semantic.min.css": "sha256-xlp3XbXCVlcKYMpXDnziC1QzN9j0SQiDjRiZ0qu6IOc=", "/js/polyfill/buffer.mjs": "sha256-RtiudSaWkLYhefdnsPMDejSmDkph66IT4I0Vk/SMTQE=", "/js/polyfill/es-arraybuffer-base64.mjs": "sha256-bOezF+Z47TCBTuteuh59UONt4sxE/wfYJWA2KY8ARrI=", "/js/polyfill/process.mjs": "sha256-VuwOUqPnkl9uyONEZ4V+TIcAUVanACvDPVCdReNzKE4=", "/js/polyfill/webassembly-function.mjs": "sha256-hiyZoW+DgOyMFu/YyOzCn/DClLfRzhj29vLf8WqqZ/4=", "/js/polyfill/websocketstream.mjs": "sha256-wy2x23eTB6tQWCiy4yMCKY++DpUKp8shr0HVRUAWfzg=", "/js/pages/index.D2Z38MUz.mjs": "sha256-SHXKWundzKSASmICef/sJQsPErwftlpi9F/R44uWG7A=", "/js/pages/logout.aPb2MrpN.mjs": "sha256-uIpBk37omV1WwqfpeWZClkYbequZuPuA4oLRC3UoWbg=", "/js/pages/preference.C2gC6k_T.mjs": "sha256-nSDF2zmsr8v3zJbCVD4vWbM8x+sui2LP+hJptY9OXg8=", "/js/pages/rot10451.D8lmvkNy.mjs": "sha256-2R1bpC283Iann28DtW3DiI8OE9HWdmFCOxBo4p128oA=", "/js/pages/chordle/list.DJJmA5kE.mjs": "sha256-ZQTygvf3FXDqjotwRf6vjNpABwoUC96v/N0jCyISf20=", "/js/pages/genkey.DOzRAMXs.mjs": "sha256-SMuIUTxVR2e8Q1UOHxSJQKKIK55fh21RJnmGYLxwTzg=", "/js/pages/login.S8X376bu.mjs": "sha256-M8VmLTc1YwEuOR0k+EWPevjlVz5rWp1WFHCGcVudasE=", "/js/pages/register.BThcd3ed.mjs": "sha256-qu0obYzuN243vk3Osloz+4IN3prMgtW3amlh6DuOzQM=", "/js/pages/change.CjltAI4r.mjs": "sha256-d9gU0utQnWPXf36f/+ehgGY/L9x/JCIAIxxHOYKwwVA=", "/js/pages/resetkey.XOqJAgvp.mjs": "sha256-oV8LtwUsYSIoIEOaSJS/3Bx9gszdEwemfX1Vasoxdqc=", "/js/pages/chordle.BvrRggp2.mjs": "sha256-EOSW2hgctKuEXt5Qe4jTGTEtLCt6rwvzbgtkuCtJd2g=", "/js/pages/leaderboard.BnLNu6SF.mjs": "sha256-9qAqQdtll9TTzzsUgZMK24mrLtZiXW4++k8wQ4sLddA=", "/js/pages/ourtimes.DEkdMbia.mjs": "sha256-JYh8eRiTwcErazxooDPCKjioPMb/IwdyheqWxD9S9jo=", "/js/pages/catcoin.DfAes_S6.mjs": "sha256-K8p0q7XZXtQXXfLRE+PwbCE1ezeQfTnbF19QjqNzA3o=", "/js/catcoin.5zr8qXu3.mjs": "sha256-hBjGtKz9pwcQEQL698LPD5EUc4rfM0p4kT2kIabM2IU=", "/js/hcaptcha.CmnYryon.mjs": "sha256-D9mCKcjn4tKvmyEI/Bpkw6CZUBGEAkxptusLPp9jqz4=", "/js/leaderboard.UYDMUIj3.mjs": "sha256-GGfK6CfFO3gq8exMNNrkQzJlVTOJhv9Da1Q8bJhFFTw=", "/js/libs.V8FxT-dX.mjs": "sha256-sdZe+xBlIGUlXoAicKrCHCnbr++5HRAU4/yPDOTCzaM=", "/css/libs.BDz4MB2e.css": "sha256-wslYAXGu+9Lynbso5vJQj1TJIuMSNQg14c0n6DLkRSw=", "/js/monaco.BI8a9-2O.mjs": "sha256-9z2UdLjlPZ6AvmlrpfNy/h5M13a4ePsk9d3B9hkAqb8=", "/css/monaco.CfsDtlrH.css": "sha256-JGIaLNOGD1nHjZa9u5A0dumSM8D5sMu8I9N7QwW7WIY=", "/js/react-katex.D0rJMOu-.mjs": "sha256-JuH1XgaDeLknE6oDAclbdsNm6etrTXK6jaDs0s5aDkw=", "/js/socket.C-bHfCZO.mjs": "sha256-YVEPQXQRwcdvrfsHuI0ItDyTbdQxknWgmMhNqhccmYQ=", "/js/wasm.DfR26pIk.mjs": "sha256-JUHdLTtQqNJLhipTaxUUd9Ke+2bhRuaYhWafCHI1tXw="}, "pages": [{"route": "/", "script": "\n\t\t\timport root from 'index';\n\t\t\timport { renderRoot } from 'libs';\n\t\t\trenderRoot(root);\n\t\t", "title": "首页"}, {"route": "/logout", "script": "\n\t\t\timport 'logout';\n\t\t", "title": "登出"}, {"route": "/preference", "script": "\n\t\t\timport root from 'preference';\n\t\t\timport { renderRoot } from 'libs';\n\t\t\trenderRoot(root);\n\t\t", "title": "偏好设置"}, {"route": "/rot10451", "script": "\n\t\t\timport root from 'rot10451';\n\t\t\timport { renderRoot } from 'libs';\n\t\t\trenderRoot(root);\n\t\t", "title": "在线 rot10451 编码"}, {"route": "/chordle/list", "script": "\n\t\t\timport root from 'chordle/list';\n\t\t\timport { renderRoot } from 'libs';\n\t\t\trenderRoot(root);\n\t\t", "title": "Chordle 谜题列表"}, {"route": "/genkey", "script": "\n\t\t\timport root from 'genkey';\n\t\t\timport { renderRoot } from 'libs';\n\t\t\trenderRoot(root);\n\t\t", "title": "在线密钥生成"}, {"route": "/login", "script": "\n\t\t\timport root from 'login';\n\t\t\timport { renderRoot } from 'libs';\n\t\t\trenderRoot(root);\n\t\t", "title": "登录信息配置"}, {"route": "/register", "script": "\n\t\t\timport root from 'register';\n\t\t\timport { renderRoot } from 'libs';\n\t\t\trenderRoot(root);\n\t\t", "title": "注册"}, {"route": "/change", "script": "\n\t\t\timport root from 'change';\n\t\t\timport { renderRoot } from 'libs';\n\t\t\trenderRoot(root);\n\t\t", "title": "修改用户信息"}, {"route": "/resetkey", "script": "\n\t\t\timport root from 'resetkey';\n\t\t\timport { renderRoot } from 'libs';\n\t\t\trenderRoot(root);\n\t\t", "title": "重置密钥"}, {"route": "/chordle", "script": "\n\t\t\timport root from 'chordle';\n\t\t\timport { renderRoot } from 'libs';\n\t\t\trenderRoot(root);\n\t\t", "title": "Chordle"}, {"route": "/sast2022/leaderboard", "script": "\n\t\t\timport root from 'leaderboard';\n\t\t\timport { renderRoot } from 'libs';\n\t\t\trenderRoot(root);\n\t\t", "title": "PyTorch Leaderboard"}, {"route": "/我的少女时代", "script": "\n\t\t\timport root from 'ourtimes';\n\t\t\timport { renderRoot } from 'libs';\n\t\t\trenderRoot(root);\n\t\t", "title": "我的少女时代"}, {"route": "/catcoin", "script": "\n\t\t\timport root from 'catcoin';\n\t\t\timport { renderRoot } from 'libs';\n\t\t\trenderRoot(root);\n\t\t", "title": "Catcoin"}]}